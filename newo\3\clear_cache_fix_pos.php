<?php
/**
 * مسح الكاش وإصلاح إعدادات POS
 * يجب تشغيله من مجلد المشروع الرئيسي
 */

echo "🔧 إصلاح نظام POS - مسح الكاش\n";
echo "=====================================\n\n";

// التأكد من وجود Laravel
if (!file_exists('artisan')) {
    echo "❌ خطأ: يجب تشغيل هذا الملف من مجلد المشروع الرئيسي\n";
    exit(1);
}

$commands = [
    'php artisan config:clear' => 'مسح كاش الإعدادات',
    'php artisan cache:clear' => 'مسح الكاش العام',
    'php artisan route:clear' => 'مسح كاش المسارات',
    'php artisan view:clear' => 'مسح كاش العروض',
    'php artisan config:cache' => 'إعادة بناء كاش الإعدادات'
];

foreach ($commands as $command => $description) {
    echo "⏳ $description...\n";
    
    $output = [];
    $returnCode = 0;
    exec($command . ' 2>&1', $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "✅ تم بنجاح\n";
    } else {
        echo "❌ فشل: " . implode("\n", $output) . "\n";
    }
    echo "\n";
}

echo "🎉 تم الانتهاء من مسح الكاش!\n";
echo "الآن يمكنك اختبار نظام POS\n";
?>
