<?php
/**
 * ملف تشخيص مشاكل نظام POS
 * يساعد في تحديد سبب رسالة "حدث خطأ في الاتصال"
 */

// إعدادات عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 تشخيص مشاكل نظام POS</h1>";
echo "<hr>";

// 1. فحص إعدادات PHP
echo "<h2>⚙️ إعدادات PHP:</h2>";
echo "<ul>";
echo "<li><strong>PHP Version:</strong> " . phpversion() . "</li>";
echo "<li><strong>Max Execution Time:</strong> " . ini_get('max_execution_time') . " seconds</li>";
echo "<li><strong>Memory Limit:</strong> " . ini_get('memory_limit') . "</li>";
echo "<li><strong>Post Max Size:</strong> " . ini_get('post_max_size') . "</li>";
echo "<li><strong>Upload Max Filesize:</strong> " . ini_get('upload_max_filesize') . "</li>";
echo "<li><strong>Max Input Vars:</strong> " . ini_get('max_input_vars') . "</li>";
echo "</ul>";

// 2. فحص الامتدادات المطلوبة
echo "<h2>📦 الامتدادات المطلوبة:</h2>";
$required_extensions = ['pdo', 'pdo_mysql', 'mysqli', 'curl', 'json', 'mbstring', 'openssl'];
echo "<ul>";
foreach ($required_extensions as $ext) {
    $status = extension_loaded($ext) ? '✅' : '❌';
    echo "<li>$status <strong>$ext:</strong> " . (extension_loaded($ext) ? 'مثبت' : 'غير مثبت') . "</li>";
}
echo "</ul>";

// 3. فحص قاعدة البيانات
echo "<h2>🗄️ اختبار قاعدة البيانات:</h2>";
try {
    // قراءة إعدادات قاعدة البيانات من .env
    $envFile = __DIR__ . '/../.env';
    if (file_exists($envFile)) {
        $envContent = file_get_contents($envFile);
        preg_match('/DB_HOST=(.*)/', $envContent, $hostMatch);
        preg_match('/DB_PORT=(.*)/', $envContent, $portMatch);
        preg_match('/DB_DATABASE=(.*)/', $envContent, $dbMatch);
        preg_match('/DB_USERNAME=(.*)/', $envContent, $userMatch);
        preg_match('/DB_PASSWORD=(.*)/', $envContent, $passMatch);
        
        $host = trim($hostMatch[1] ?? '127.0.0.1');
        $port = trim($portMatch[1] ?? '3306');
        $database = trim($dbMatch[1] ?? '');
        $username = trim($userMatch[1] ?? 'root');
        $password = trim($passMatch[1] ?? '');
    } else {
        throw new Exception('ملف .env غير موجود');
    }
    
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات ناجح!</p>";
    echo "<ul>";
    echo "<li><strong>قاعدة البيانات:</strong> $database</li>";
    echo "<li><strong>الخادم:</strong> $host:$port</li>";
    
    // فحص الجداول المطلوبة
    $requiredTables = ['pos', 'pos_payments', 'pos_products', 'product_services', 'customers', 'warehouses'];
    echo "<li><strong>الجداول المطلوبة:</strong></li>";
    echo "<ul>";
    foreach ($requiredTables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "<li>✅ $table: $count سجل</li>";
        } catch (Exception $e) {
            echo "<li>❌ $table: غير موجود أو خطأ</li>";
        }
    }
    echo "</ul>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
}

// 4. فحص الملفات المطلوبة
echo "<h2>📁 فحص الملفات المطلوبة:</h2>";
$requiredFiles = [
    '../app/Http/Controllers/PosController.php',
    '../routes/web.php',
    '../resources/views/pos/index.blade.php',
    '../resources/views/pos/show.blade.php'
];

echo "<ul>";
foreach ($requiredFiles as $file) {
    $exists = file_exists(__DIR__ . '/' . $file);
    $status = $exists ? '✅' : '❌';
    echo "<li>$status $file: " . ($exists ? 'موجود' : 'غير موجود') . "</li>";
}
echo "</ul>";

// 5. فحص الصلاحيات
echo "<h2>🔐 فحص صلاحيات الملفات:</h2>";
$directories = [
    '../storage/logs',
    '../storage/framework/sessions',
    '../storage/framework/cache',
    '../bootstrap/cache'
];

echo "<ul>";
foreach ($directories as $dir) {
    $path = __DIR__ . '/' . $dir;
    if (is_dir($path)) {
        $writable = is_writable($path);
        $status = $writable ? '✅' : '❌';
        echo "<li>$status $dir: " . ($writable ? 'قابل للكتابة' : 'غير قابل للكتابة') . "</li>";
    } else {
        echo "<li>❌ $dir: غير موجود</li>";
    }
}
echo "</ul>";

// 6. اختبار AJAX
echo "<h2>🌐 اختبار AJAX:</h2>";
echo "<button onclick='testAjax()' class='btn btn-primary'>اختبار طلب AJAX</button>";
echo "<div id='ajax-result' style='margin-top: 10px;'></div>";

// 7. فحص السجلات
echo "<h2>📋 آخر الأخطاء من السجلات:</h2>";
$logFile = __DIR__ . '/../storage/logs/laravel.log';
if (file_exists($logFile)) {
    $logs = file_get_contents($logFile);
    $lines = explode("\n", $logs);
    $recentLines = array_slice($lines, -20); // آخر 20 سطر
    
    echo "<div style='background: #f5f5f5; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto;'>";
    echo "<pre>" . implode("\n", $recentLines) . "</pre>";
    echo "</div>";
} else {
    echo "<p>ملف السجلات غير موجود</p>";
}

?>

<script>
function testAjax() {
    const resultDiv = document.getElementById('ajax-result');
    resultDiv.innerHTML = '⏳ جاري الاختبار...';
    
    fetch(window.location.href, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({test: 'ajax'})
    })
    .then(response => {
        if (response.ok) {
            resultDiv.innerHTML = '✅ طلب AJAX يعمل بشكل طبيعي';
            resultDiv.style.color = 'green';
        } else {
            resultDiv.innerHTML = '❌ خطأ في طلب AJAX: ' + response.status;
            resultDiv.style.color = 'red';
        }
    })
    .catch(error => {
        resultDiv.innerHTML = '❌ خطأ في الشبكة: ' + error.message;
        resultDiv.style.color = 'red';
    });
}
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2 { color: #333; }
ul { margin: 10px 0; }
li { margin: 5px 0; }
.btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
.btn:hover { background: #0056b3; }
</style>
