/**
 * معالج أخطاء محسن لنظام POS
 * يساعد في حل مشاكل الاتصال والأخطاء الشائعة
 */

(function() {
    'use strict';
    
    // إعدادات معالج الأخطاء
    const POS_ERROR_HANDLER = {
        maxRetries: 3,
        retryDelay: 2000,
        timeout: 60000,
        debug: false
    };
    
    // دالة تسجيل الأخطاء
    function logError(message, data = {}) {
        if (POS_ERROR_HANDLER.debug) {
            console.error('[POS Error Handler]', message, data);
        }
        
        // إرسال الخطأ إلى السيرفر للتسجيل
        try {
            fetch('/api/log-error', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    message: message,
                    data: data,
                    url: window.location.href,
                    userAgent: navigator.userAgent,
                    timestamp: new Date().toISOString()
                })
            }).catch(() => {}); // تجاهل أخطاء التسجيل
        } catch (e) {
            // تجاهل أخطاء التسجيل
        }
    }
    
    // دالة إعادة المحاولة
    function retryRequest(originalFunction, params, attempt = 1) {
        return new Promise((resolve, reject) => {
            if (attempt > POS_ERROR_HANDLER.maxRetries) {
                reject(new Error('تم تجاوز الحد الأقصى لعدد المحاولات'));
                return;
            }
            
            setTimeout(() => {
                originalFunction(params)
                    .then(resolve)
                    .catch(error => {
                        logError(`محاولة ${attempt} فشلت`, { error: error.message, params });
                        retryRequest(originalFunction, params, attempt + 1)
                            .then(resolve)
                            .catch(reject);
                    });
            }, attempt > 1 ? POS_ERROR_HANDLER.retryDelay : 0);
        });
    }
    
    // تحسين jQuery AJAX إذا كان متوفراً
    if (typeof $ !== 'undefined' && $.ajaxSetup) {
        // إعدادات AJAX عامة محسنة
        $.ajaxSetup({
            timeout: POS_ERROR_HANDLER.timeout,
            cache: false,
            beforeSend: function(xhr, settings) {
                // إضافة headers مطلوبة
                xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
                
                // إضافة CSRF token إذا كان متوفراً
                const token = $('meta[name="csrf-token"]').attr('content');
                if (token) {
                    xhr.setRequestHeader('X-CSRF-TOKEN', token);
                }
                
                logError('AJAX Request Started', {
                    url: settings.url,
                    method: settings.type,
                    data: settings.data
                });
            }
        });
        
        // معالج أخطاء AJAX محسن
        $(document).ajaxError(function(event, xhr, settings, thrownError) {
            const errorData = {
                url: settings.url,
                method: settings.type,
                status: xhr.status,
                statusText: xhr.statusText,
                responseText: xhr.responseText,
                thrownError: thrownError
            };
            
            logError('AJAX Error Occurred', errorData);
            
            // تحديد نوع الخطأ ورسالة مناسبة
            let errorMessage = 'حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.';
            
            switch (xhr.status) {
                case 0:
                    errorMessage = 'فشل في الاتصال بالخادم. يرجى التحقق من الاتصال بالإنترنت.';
                    break;
                case 400:
                    errorMessage = 'طلب غير صحيح. يرجى التحقق من البيانات المدخلة.';
                    break;
                case 401:
                    errorMessage = 'انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.';
                    // إعادة توجيه لصفحة تسجيل الدخول
                    setTimeout(() => {
                        window.location.href = '/login';
                    }, 2000);
                    break;
                case 403:
                    errorMessage = 'ليس لديك صلاحية للوصول إلى هذه الخدمة.';
                    break;
                case 404:
                    errorMessage = 'الخدمة المطلوبة غير موجودة.';
                    break;
                case 419:
                    errorMessage = 'انتهت صلاحية الصفحة. يرجى إعادة تحميل الصفحة.';
                    // إعادة تحميل الصفحة
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                    break;
                case 422:
                    errorMessage = 'بيانات غير صحيحة. يرجى التحقق من البيانات المدخلة.';
                    break;
                case 500:
                    errorMessage = 'خطأ في الخادم. يرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني.';
                    break;
                case 502:
                case 503:
                case 504:
                    errorMessage = 'الخادم غير متاح حالياً. يرجى المحاولة مرة أخرى لاحقاً.';
                    break;
                default:
                    if (thrownError === 'timeout') {
                        errorMessage = 'انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى.';
                    }
            }
            
            // عرض رسالة الخطأ إذا كان الطلب متعلق بـ POS
            if (settings.url && (settings.url.includes('pos') || settings.url.includes('payment'))) {
                if (typeof show_toastr === 'function') {
                    show_toastr('error', errorMessage, 'error');
                } else {
                    alert(errorMessage);
                }
            }
        });
    }
    
    // معالج أخطاء JavaScript العامة
    window.addEventListener('error', function(event) {
        logError('JavaScript Error', {
            message: event.message,
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
            stack: event.error ? event.error.stack : null
        });
    });
    
    // معالج الأخطاء غير المعالجة في Promise
    window.addEventListener('unhandledrejection', function(event) {
        logError('Unhandled Promise Rejection', {
            reason: event.reason,
            stack: event.reason ? event.reason.stack : null
        });
    });
    
    // دالة مساعدة لإرسال طلبات POS محسنة
    window.sendPOSRequest = function(url, data, options = {}) {
        const defaultOptions = {
            method: 'POST',
            timeout: POS_ERROR_HANDLER.timeout,
            retries: POS_ERROR_HANDLER.maxRetries
        };
        
        const finalOptions = Object.assign(defaultOptions, options);
        
        return new Promise((resolve, reject) => {
            const makeRequest = (attempt = 1) => {
                $.ajax({
                    url: url,
                    method: finalOptions.method,
                    data: data,
                    timeout: finalOptions.timeout,
                    success: function(response) {
                        logError('POS Request Success', { url, attempt, response });
                        resolve(response);
                    },
                    error: function(xhr, status, error) {
                        const errorInfo = {
                            url: url,
                            attempt: attempt,
                            status: xhr.status,
                            error: error,
                            responseText: xhr.responseText
                        };
                        
                        logError('POS Request Failed', errorInfo);
                        
                        if (attempt < finalOptions.retries && xhr.status >= 500) {
                            // إعادة المحاولة للأخطاء الخادم فقط
                            setTimeout(() => {
                                makeRequest(attempt + 1);
                            }, POS_ERROR_HANDLER.retryDelay);
                        } else {
                            reject(xhr);
                        }
                    }
                });
            };
            
            makeRequest();
        });
    };
    
    // تهيئة معالج الأخطاء عند تحميل الصفحة
    $(document).ready(function() {
        logError('POS Error Handler Initialized', {
            url: window.location.href,
            userAgent: navigator.userAgent,
            timestamp: new Date().toISOString()
        });
        
        // فحص متطلبات النظام
        const requirements = {
            jquery: typeof $ !== 'undefined',
            csrfToken: $('meta[name="csrf-token"]').length > 0,
            localStorage: typeof Storage !== 'undefined'
        };
        
        logError('System Requirements Check', requirements);
        
        // تحذير إذا كانت هناك متطلبات مفقودة
        const missingRequirements = Object.keys(requirements).filter(key => !requirements[key]);
        if (missingRequirements.length > 0) {
            console.warn('[POS Error Handler] Missing requirements:', missingRequirements);
        }
    });
    
})();
