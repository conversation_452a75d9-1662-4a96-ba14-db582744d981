<?php
/**
 * اختبار بسيط لفحص اتصال نظام POS
 */

// إعدادات عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 اختبار اتصال نظام POS</h1>";
echo "<hr>";

// فحص إذا كان الطلب AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'success',
        'message' => 'AJAX connection working',
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit;
}

// فحص قاعدة البيانات
echo "<h2>🗄️ فحص قاعدة البيانات:</h2>";
try {
    // قراءة إعدادات قاعدة البيانات
    $envFile = __DIR__ . '/../.env';
    if (file_exists($envFile)) {
        $envContent = file_get_contents($envFile);
        preg_match('/DB_HOST=(.*)/', $envContent, $hostMatch);
        preg_match('/DB_DATABASE=(.*)/', $envContent, $dbMatch);
        preg_match('/DB_USERNAME=(.*)/', $envContent, $userMatch);
        preg_match('/DB_PASSWORD=(.*)/', $envContent, $passMatch);
        
        $host = trim($hostMatch[1] ?? '127.0.0.1');
        $database = trim($dbMatch[1] ?? '');
        $username = trim($userMatch[1] ?? 'root');
        $password = trim($passMatch[1] ?? '');
    } else {
        throw new Exception('ملف .env غير موجود');
    }
    
    $dsn = "mysql:host=$host;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات ناجح!</p>";
    
    // فحص جدول pos
    $stmt = $pdo->query("SELECT COUNT(*) FROM pos");
    $posCount = $stmt->fetchColumn();
    echo "<p>📊 عدد فواتير POS: $posCount</p>";
    
    // فحص آخر فاتورة
    $stmt = $pdo->query("SELECT * FROM pos ORDER BY id DESC LIMIT 1");
    $lastPos = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($lastPos) {
        echo "<p>🧾 آخر فاتورة: رقم {$lastPos['pos_id']} بتاريخ {$lastPos['pos_date']}</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

// فحص الجلسة
echo "<h2>🔐 فحص الجلسة:</h2>";
session_start();
if (isset($_SESSION)) {
    echo "<p style='color: green;'>✅ الجلسة تعمل بشكل طبيعي</p>";
    echo "<p>🆔 معرف الجلسة: " . session_id() . "</p>";
    
    // فحص بيانات POS في الجلسة
    if (isset($_SESSION['pos'])) {
        echo "<p>🛒 بيانات POS في الجلسة: " . count($_SESSION['pos']) . " منتج</p>";
    } else {
        echo "<p>🛒 لا توجد بيانات POS في الجلسة</p>";
    }
} else {
    echo "<p style='color: red;'>❌ مشكلة في الجلسة</p>";
}

// اختبار AJAX
echo "<h2>🌐 اختبار AJAX:</h2>";
echo "<button onclick='testPosAjax()' style='padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;'>اختبار طلب POS AJAX</button>";
echo "<div id='ajax-result' style='margin-top: 10px; padding: 10px; border-radius: 5px;'></div>";

// فحص ملفات Laravel
echo "<h2>📁 فحص ملفات Laravel:</h2>";
$laravelFiles = [
    '../app/Http/Controllers/PosController.php' => 'POS Controller',
    '../routes/web.php' => 'Web Routes',
    '../config/app.php' => 'App Config'
];

foreach ($laravelFiles as $file => $name) {
    $exists = file_exists(__DIR__ . '/' . $file);
    $status = $exists ? '✅' : '❌';
    echo "<p>$status $name: " . ($exists ? 'موجود' : 'غير موجود') . "</p>";
}

?>

<script>
function testPosAjax() {
    const resultDiv = document.getElementById('ajax-result');
    resultDiv.innerHTML = '⏳ جاري اختبار AJAX...';
    resultDiv.style.background = '#f0f0f0';
    resultDiv.style.color = '#333';
    
    // محاكاة طلب POS
    const testData = {
        vc_name: 0,
        warehouse_name: 1,
        discount: 0,
        quotation_id: 0,
        _token: 'test_token'
    };
    
    fetch(window.location.href, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify(testData)
    })
    .then(response => {
        if (response.ok) {
            return response.json();
        } else {
            throw new Error('HTTP ' + response.status);
        }
    })
    .then(data => {
        resultDiv.innerHTML = '✅ طلب AJAX نجح: ' + JSON.stringify(data);
        resultDiv.style.background = '#d4edda';
        resultDiv.style.color = '#155724';
    })
    .catch(error => {
        resultDiv.innerHTML = '❌ خطأ في AJAX: ' + error.message;
        resultDiv.style.background = '#f8d7da';
        resultDiv.style.color = '#721c24';
    });
}

// اختبار تلقائي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔍 صفحة اختبار POS محملة');
    
    // فحص jQuery إذا كان متوفر
    if (typeof $ !== 'undefined') {
        console.log('✅ jQuery متوفر');
    } else {
        console.log('❌ jQuery غير متوفر');
    }
    
    // فحص CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]');
    if (csrfToken) {
        console.log('✅ CSRF Token موجود');
    } else {
        console.log('❌ CSRF Token غير موجود');
    }
});
</script>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    background: #f8f9fa;
}
h1, h2 { 
    color: #333; 
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}
p { 
    margin: 10px 0; 
    padding: 5px;
}
button:hover { 
    background: #0056b3 !important; 
}
</style>
