{"__meta": {"id": "X7d0bf12f047bfd59240d0f92baa3ff86", "datetime": "2025-06-17 12:06:48", "utime": **********.437378, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750162007.713364, "end": **********.437412, "duration": 0.7240481376647949, "duration_str": "724ms", "measures": [{"label": "Booting", "start": 1750162007.713364, "relative_start": 0, "end": **********.331179, "relative_end": **********.331179, "duration": 0.6178150177001953, "duration_str": "618ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.331194, "relative_start": 0.6178300380706787, "end": **********.437416, "relative_end": 4.0531158447265625e-06, "duration": 0.10622215270996094, "duration_str": "106ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45405232, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1688\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1688-1698</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01734, "accumulated_duration_str": "17.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3909938, "duration": 0.01585, "duration_str": "15.85ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.407}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.423059, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.407, "width_percent": 8.593}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-375297606 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-375297606\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2068335533 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2068335533\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1380625970 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1380625970\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=1ta1y4p%7C1750157552093%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlRIdDFGSTJsdUhsUisvSDE4QU5lRVE9PSIsInZhbHVlIjoiL2tFemZXdkJ1UnlNSFZ2OEgrdnpoNDhpU0Q0cVdNdjMyM1lxVFl5T2RPRFY1emFsUU5lQ21GMmUrMk5LUmR1VDVKSmNLWlA2MDNZOEh0L1dtZFRxbW1oaU5sbWhydlNmd3ViZVVFZjNOeDB3RDRuRG41dFlkVmZYYXpTUkNOOERTdGZ4QnZ5YTR3SWJadG9LODVWcUlyR1lKdFFRanpzcVZtUkl2cDFEQllwMVJxMEp3TGpXeVJLK3daNGJPOFEyOGF3Z3BRQTFTSXQ5aEw3WHhRbjFkaWI1RDA1QnY0dDZ2bEg5VVoyS2psdEMwSU81VStWdjBmZzUrZlBIQmlHNks0TFRVRmU0NFl2alNneDVzWTNOdDJ2aWZDU2pPcit2NmR6Z1o0ckF5WDB2Q3l1SVJDdnZrMmFDdGZ2OFIvaVdObllMUUtFd1MzaXlyWmtWWERDc3g3cnlZd2Z1ZGJLdmZBQ0VId0lORzd1L1E4QjBGRDV6NnVwRjZFN0RHNG8zVWZnNWpuTDFBVmJoMkI0aVVkcUtWdmUybzFWV3NqRys3cmI4STUzaVRzaGhsT1prbmpHS2xndVUzblBBZXMyd29wR2R2d3Yyd0Y2NmNGaEtZSFhEdGhjNk04WS9Kai9JTEZ0OUhoNXFFeDVxU0N2QzJTaGxoRE14UGV1aDgxaTgiLCJtYWMiOiJlODMyY2I0Y2QzYzcxZmMxZTlmZGNhMmQ4MDFiNGE5NTQ1NWYwNzA1YmFkODg5NDIwMWE5OWRkMjg0MzMyMDc5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InpidW1zS2NZa1BtazJnVzI3MXlXeHc9PSIsInZhbHVlIjoiVVA4MnczT3JTVGJVaTBtdkdkdW9Gd1dYd1NEMk5IMTVkMGlVUXpQbWxDSzhkWTJQVThhYlZrbHNqUS9velBHeTd1TEY3dXorR2tMTEkvZzQvZmRnWFpROFRWSDFnQjBWak9yc0Y3THBiUzRLVnZpQWVscVRNMmVnbFptSEU4NTR0aGJKcjBTMmp5NGM5cmpleTRUcStINjlVMlZjUW9TNXI5T05DWitXaVVWK3FDOFFoU1JlQThSQmF6dTVLZmllWGw3VWc0Um8rL3NQekVkK0VlZU5EZzhDRHYxQ0JHZW9RMy9Md0dBa1gvakd0aHo2UDhlaXd5YU5FREdiNlRGQWw1c3lhRWJBVVBzMEVsazJ0N05kMTZuODM2WWpXQWU3M0JlL3F1TVBGMGF5Z2dNMDVRMGpLUTVkdGhyVVlzbVJpODdzU0N1b0F4RG1teE5sTjFITU9hTTNMRDAzMkpvaUFFNWRLSy9IaUxFMzJnbmhsWFVxWVJnRFdLTytzNjJiOVVXVU5KUFV0ekhTKzRtN0RkMGZqSzVhY3VWU3krUjg2cmhYUDlGMmxJNnJtZzMvOXQ2YUxmMTN2RHFvaWMwelpscnNuMjhkUGUzNTAvVGNidWNOMUdoRmRhbkFmSk9PUStBTEp1NFpoZTJDeTJLWnltRU9GU2hTdUFEUlB3VXYiLCJtYWMiOiJkNWY3MDViODg3MjhkN2Y0N2E1ODRkNDNkM2Y1NGQ5ZTFkNWFjMmI4ZDI1MTY1Y2M4NDhjMTc1OTE3MDVmZmY3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1811672238 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">x6J25WmpCphH0nVhGWCYfOOOMFtTSb6zPGzaM2hl</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1811672238\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-797752067 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 12:06:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFIRUJMNWN3TUZhdW1JSVQ1dkRkSkE9PSIsInZhbHVlIjoiYXpsTmlKa1VvL2tMbWZtVnIyZE16bFhoVnhxVGJZMlhIMU1sQzEzS3UvUGhuN25EVS9DS3BrSVlIQ21XL1Z3cDMzYmJDQ3VlSmRkODJMWFlsdksvY2gvV3pTMUdrdUtqSnBSRFVHcjZSdUFiWU1pSGp4eHE0UlcwQVVmZ2cvcmRDclo1SEthN2RKL0JCeEZOcmZ3K2w2YW9rUlhxei9uNjVoNDZQVTlCZlFxRUw0STROMzhGOXNPcFFQUU1TdmxhZ2hFdnRqOWJIb29QV0NZYVRKNk1HQUlyTmxzbWI3WHZiYlBCb1poWnFzRmtjMWJzUDcveHRiRkkxS0liYWZIQkk1dnFEdWJ3ZjE3bnRRcExQTHhkVXRMZGJzRHFBQ1d0eWNNSWExK0xVWm9CZnBmQytjbnRiSTNhNGFiSmQ5ZUdob2dvY25YWlhnOGxGYnFsY2pDVkNqTjlyeU04MG5yRWRZQldkcDBrM29SQjZlUmpIdTdNVDZwM3BpNVB2b1VzSU0vMDYvandGOWEzM3J3NzVMQWplNmlNMWdTUEFhQU1LQVJZYlYyOHpXVDd6RUF2dDVENzJ2RjNDMHJnVmY4aVQ0OUQ3d2cwR3N3TFhIZXBEYmNybTRQNzJPTk9JK0RCMWhhMFhURmhMSG1jZ2dOejFqTTdGSFBwS1dxSThUTEQiLCJtYWMiOiJjOGJiNzg3ZTFmOWEyZGQxMjQ1Njk5OGE3MDFjYWI0M2JiMjk2YWUyZjE5Zjc3Yzk1N2IwZTk4MWFhZWFjYmViIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:06:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkNoSkJwS3FYMENJYUhTWi8rMVFiUGc9PSIsInZhbHVlIjoiZnZWK1IyUW5idnRCYlV0UGl2a1V0aUtlTC9FQU5RREYzOHoyd1Q3WndOU25iamFtSkdyYW1QL2FqOUFzTzJUYnZaamZadWZESTVaMFFCNENWOFFseHl1M3JwY3VmUFhHZENMZ3p4dmd5R2VJTU5IaXhnek93aXFqNVdPK0hWMnpWWmpDQVVZSjdKcEJkMy91N3hVbHFkc3l1aDJUekdkaXJ0ZXlLM3ZPV09MRlJNeFVqV0c0M0J1ai8vOThwNFk0ZlF6bUNtS2tLVk1IOFdPN1E0VmZZeFhZdk1CSmJjRVMyK0NRb01GRDBYWXVtTWdaaTZTbHhzQVh3d25aSEZYSS9KaEIwbGtvTzhpeE5Ka0FheDFsMDlXT1ZTemxRZkJqU2x6Vkg1S2p1emFxS3ZxMDB6RWlBMHUreUpTSWl3SjVidXZUTXhLRFhJWW96Z1hzRGpaZHhqTEx5OHZDYmJsUFlDU0xGbEQrUWdnS3dmRXNOdGUwYkE2MnVGSkZRY3dPeEtBNjl6aDFHeWozeG9kUFdBRzFJZ3g5bks3djk0Q1BTZ1BUZVUxbTVQL1FWUWlmdGt1cTRHdTNONnN0c2lJbktVUWx3ZnRJemU1L2R5UDdrS1dHK3NQQjY4U0xmdFBaVHVnVEFLc2JTYWNNVjZUWnB4ZnBHWGJFdzVxYjcyOXIiLCJtYWMiOiJmYjM2YWQ5NTQ3NTc2ZGY3YTIzYjczMTNiYWU4NDY5ZTFiYjU5YjFlODdiODVlMDI3NzIwYjlmZDYzMjAyMjNmIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:06:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFIRUJMNWN3TUZhdW1JSVQ1dkRkSkE9PSIsInZhbHVlIjoiYXpsTmlKa1VvL2tMbWZtVnIyZE16bFhoVnhxVGJZMlhIMU1sQzEzS3UvUGhuN25EVS9DS3BrSVlIQ21XL1Z3cDMzYmJDQ3VlSmRkODJMWFlsdksvY2gvV3pTMUdrdUtqSnBSRFVHcjZSdUFiWU1pSGp4eHE0UlcwQVVmZ2cvcmRDclo1SEthN2RKL0JCeEZOcmZ3K2w2YW9rUlhxei9uNjVoNDZQVTlCZlFxRUw0STROMzhGOXNPcFFQUU1TdmxhZ2hFdnRqOWJIb29QV0NZYVRKNk1HQUlyTmxzbWI3WHZiYlBCb1poWnFzRmtjMWJzUDcveHRiRkkxS0liYWZIQkk1dnFEdWJ3ZjE3bnRRcExQTHhkVXRMZGJzRHFBQ1d0eWNNSWExK0xVWm9CZnBmQytjbnRiSTNhNGFiSmQ5ZUdob2dvY25YWlhnOGxGYnFsY2pDVkNqTjlyeU04MG5yRWRZQldkcDBrM29SQjZlUmpIdTdNVDZwM3BpNVB2b1VzSU0vMDYvandGOWEzM3J3NzVMQWplNmlNMWdTUEFhQU1LQVJZYlYyOHpXVDd6RUF2dDVENzJ2RjNDMHJnVmY4aVQ0OUQ3d2cwR3N3TFhIZXBEYmNybTRQNzJPTk9JK0RCMWhhMFhURmhMSG1jZ2dOejFqTTdGSFBwS1dxSThUTEQiLCJtYWMiOiJjOGJiNzg3ZTFmOWEyZGQxMjQ1Njk5OGE3MDFjYWI0M2JiMjk2YWUyZjE5Zjc3Yzk1N2IwZTk4MWFhZWFjYmViIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:06:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkNoSkJwS3FYMENJYUhTWi8rMVFiUGc9PSIsInZhbHVlIjoiZnZWK1IyUW5idnRCYlV0UGl2a1V0aUtlTC9FQU5RREYzOHoyd1Q3WndOU25iamFtSkdyYW1QL2FqOUFzTzJUYnZaamZadWZESTVaMFFCNENWOFFseHl1M3JwY3VmUFhHZENMZ3p4dmd5R2VJTU5IaXhnek93aXFqNVdPK0hWMnpWWmpDQVVZSjdKcEJkMy91N3hVbHFkc3l1aDJUekdkaXJ0ZXlLM3ZPV09MRlJNeFVqV0c0M0J1ai8vOThwNFk0ZlF6bUNtS2tLVk1IOFdPN1E0VmZZeFhZdk1CSmJjRVMyK0NRb01GRDBYWXVtTWdaaTZTbHhzQVh3d25aSEZYSS9KaEIwbGtvTzhpeE5Ka0FheDFsMDlXT1ZTemxRZkJqU2x6Vkg1S2p1emFxS3ZxMDB6RWlBMHUreUpTSWl3SjVidXZUTXhLRFhJWW96Z1hzRGpaZHhqTEx5OHZDYmJsUFlDU0xGbEQrUWdnS3dmRXNOdGUwYkE2MnVGSkZRY3dPeEtBNjl6aDFHeWozeG9kUFdBRzFJZ3g5bks3djk0Q1BTZ1BUZVUxbTVQL1FWUWlmdGt1cTRHdTNONnN0c2lJbktVUWx3ZnRJemU1L2R5UDdrS1dHK3NQQjY4U0xmdFBaVHVnVEFLc2JTYWNNVjZUWnB4ZnBHWGJFdzVxYjcyOXIiLCJtYWMiOiJmYjM2YWQ5NTQ3NTc2ZGY3YTIzYjczMTNiYWU4NDY5ZTFiYjU5YjFlODdiODVlMDI3NzIwYjlmZDYzMjAyMjNmIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:06:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-797752067\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1113505204 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1113505204\", {\"maxDepth\":0})</script>\n"}}