<?php

return [
    /*
    |--------------------------------------------------------------------------
    | POS System Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the POS system
    | to improve performance and handle connection issues.
    |
    */

    // AJAX timeout settings (in milliseconds)
    'ajax_timeout' => env('POS_AJAX_TIMEOUT', 60000), // زيادة المهلة إلى 60 ثانية

    // Database query timeout (in seconds)
    'db_timeout' => env('POS_DB_TIMEOUT', 60), // زيادة مهلة قاعدة البيانات
    
    // Maximum products to load per page
    'products_per_page' => env('POS_PRODUCTS_PER_PAGE', 50),
    
    // Cache settings
    'cache_products' => env('POS_CACHE_PRODUCTS', true),
    'cache_duration' => env('POS_CACHE_DURATION', 300), // 5 minutes
    
    // Error handling
    'show_detailed_errors' => env('POS_SHOW_DETAILED_ERRORS', false),
    'log_ajax_errors' => env('POS_LOG_AJAX_ERRORS', true),
    
    // Connection retry settings
    'max_retries' => env('POS_MAX_RETRIES', 3),
    'retry_delay' => env('POS_RETRY_DELAY', 1000), // milliseconds
    
    // Image settings
    'default_product_image' => 'uploads/pro_image/default.png',
    'image_lazy_loading' => env('POS_IMAGE_LAZY_LOADING', true),
    
    // Performance settings
    'enable_compression' => env('POS_ENABLE_COMPRESSION', true),
    'minify_responses' => env('POS_MINIFY_RESPONSES', false),
    
    // Security settings
    'csrf_protection' => env('POS_CSRF_PROTECTION', true),
    'rate_limiting' => env('POS_RATE_LIMITING', true),

    // Server compatibility settings
    'force_https' => env('POS_FORCE_HTTPS', false),
    'debug_mode' => env('POS_DEBUG_MODE', false),
    'log_requests' => env('POS_LOG_REQUESTS', true),

    // Connection settings for production servers
    'connection_retry_attempts' => env('POS_CONNECTION_RETRY_ATTEMPTS', 3),
    'connection_retry_delay' => env('POS_CONNECTION_RETRY_DELAY', 2000), // milliseconds
    
    // Search settings
    'search_min_length' => env('POS_SEARCH_MIN_LENGTH', 1),
    'search_delay' => env('POS_SEARCH_DELAY', 300), // milliseconds
    
    // Cart settings
    'auto_save_cart' => env('POS_AUTO_SAVE_CART', true),
    'cart_session_lifetime' => env('POS_CART_SESSION_LIFETIME', 120), // minutes
];
