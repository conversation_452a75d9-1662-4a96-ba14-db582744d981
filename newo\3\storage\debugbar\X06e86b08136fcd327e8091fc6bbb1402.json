{"__meta": {"id": "X06e86b08136fcd327e8091fc6bbb1402", "datetime": "2025-06-17 12:06:44", "utime": **********.957611, "method": "GET", "uri": "/product-categories?_=**********178", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.340058, "end": **********.957636, "duration": 0.6175780296325684, "duration_str": "618ms", "measures": [{"label": "Booting", "start": **********.340058, "relative_start": 0, "end": **********.813804, "relative_end": **********.813804, "duration": 0.47374582290649414, "duration_str": "474ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.813816, "relative_start": 0.4737579822540283, "end": **********.957638, "relative_end": 1.9073486328125e-06, "duration": 0.14382195472717285, "duration_str": "144ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48278000, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.0254, "accumulated_duration_str": "25.4ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.869143, "duration": 0.01498, "duration_str": "14.98ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 58.976}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.896241, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 58.976, "width_percent": 3.071}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.918381, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 62.047, "width_percent": 7.087}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.922721, "duration": 0.00237, "duration_str": "2.37ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 69.134, "width_percent": 9.331}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.933505, "duration": 0.00443, "duration_str": "4.43ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 78.465, "width_percent": 17.441}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.943441, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 95.906, "width_percent": 4.094}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-841103409 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-841103409\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.930142, "xdebug_link": null}]}, "session": {"_token": "8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-706963734 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-706963734\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1732116438 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">**********178</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1732116438\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1993581138 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1993581138\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1047132121 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=1ta1y4p%7C1750157552093%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkhrYXBoUDBZZ21sRTcyUlBEV3BKYnc9PSIsInZhbHVlIjoiR0g0RTRDNFFzZUMzR3V2aDBSNGtNVDBITEpKdEw5MlBxR25VVytLcitmbS9PN1kvbU1iR1B0N3diRktUUGx6eUlEenVQeHBuNkQzNnNrMnFVZkNpaEZyWE45clJIK2pnQ2NMK1dDWEpLV04vaGxHYjZGUkRTRTBGWEM3V2dDakhpcVNIakVJaVNvdjZRSS82bkluL2pqQ0JJRG9vTWlmRUlTWkFWVFpWWGF4QnRkYlE3NG12RHpvM0V3ZlF3WGNDMjQrUTVCVFNCd1BhTlJjK3E0ZjI0czNhWlBGWTZaWVMwSjYwbFJwL25ITzVGS3ViMkFTaHFha0IxRG9PME1iNXNPSCtsajFkNjdvZ0ZleVI0cGZ5Wmc3S21TM2dQYklrckRmckZkMllIaG1MSCs2VWR4T1lrVXJCTmgvM2JjMC9oeFVTYlJMTHprR3lKRWRjakM5MU5Ld0ltSlNNYjdxZzFGWUFtb0dhbDZFM1VGL0p0T2VtbzFIY3lNTlhnN3U5Zlh0dE9qcGNIa1FxdnVMR0QxOTNiN1BhdDJBd2JDSGpIYWE1dlJiTndWMDZmN0VCVERCczRjMUNLdGpiMHA1UDk4dEJlOXVMOXNrcndvL0JXZG9VZ05Db0R0SUVNektnS2hpbnczU1VHa01YYmh6M3pkU1VXd3QwcEh6UVJaaTMiLCJtYWMiOiJhYjFhYTA5MjdmMGMxMjU5NGIwYmI4Y2UwN2I0N2Q3MWE2Y2RlZmRlNDk4NmY0YzVjYzkxMTg0MGU3MmQwMWNjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjdPTkpDN0pIWGgwRUFhakxEeFF6b3c9PSIsInZhbHVlIjoiZUdvQVBna3RRNkVta1k1NjdjRk5PNW45ZjBOL1N3c2U3c0phK2ZqNXIvSFlobC9WTCtkdEp4UHBmcUMxaEJYSnNiekl1RzJndTBCc0I5UjA0M3cxQXJJZmk5TUhpdDcySWNVY1BLSnZlVWhoTVlPMG9NdEI4WlU0Z285TFMrT3JMSW1QbXZhVUdPK1lZUnpCcWlSclkzYy9wNHIxbnpFOGM5a2ZHOFNqQWRrUVBnTkgza2lRYTIwRjVwRW51c2FwSnVXc3hXdG9hdUlONDNSSjBUc0JUTnMyUHptVWZXdHZRRmNuZk5CMk9OejZwL25jcVRsZUlvNGhKbldHVE4xOFpia1loc0ErSzV6cWdQUWJuQXJveXFxNGJYN3YzWGNnVi9TVE15Zm0rODZrWGVWUnhZcjBXRW1ZRE1FNjEyYmY5a1JoMlNid09QT1gweWZPNXJPdStPcUpNbDBjdUs0a1pXT3ZjSndzSzJrRmFsMnIzbGxSdjRPT3lPdGczS084djdnTWlOWjFVMGNEazRuWm5PSkdkMGVmWnFORWFJQkJ4RXRCbEU3SE9saW5hYzNLR3dxSzFpVGZ4ajJ2RTkrOWYybFBuRWZZV29JWTR0LzU1dmU0Tk4zSmJ2ODhnZmx0RkNjaGhNVGdkc0ROdFQ3VXU3aEU5Z3NOMW5SUUE1S1YiLCJtYWMiOiI4YjI2MjVjNGZmNGJiYWQ2NmUzYjYzMmE3Y2I3MTI4NGVhOTNhZmY5NDY2OGUyM2ZiMzEzYTNlNTgxYmYyOTJlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1047132121\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1349141768 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">x6J25WmpCphH0nVhGWCYfOOOMFtTSb6zPGzaM2hl</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1349141768\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 12:06:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlRIdDFGSTJsdUhsUisvSDE4QU5lRVE9PSIsInZhbHVlIjoiL2tFemZXdkJ1UnlNSFZ2OEgrdnpoNDhpU0Q0cVdNdjMyM1lxVFl5T2RPRFY1emFsUU5lQ21GMmUrMk5LUmR1VDVKSmNLWlA2MDNZOEh0L1dtZFRxbW1oaU5sbWhydlNmd3ViZVVFZjNOeDB3RDRuRG41dFlkVmZYYXpTUkNOOERTdGZ4QnZ5YTR3SWJadG9LODVWcUlyR1lKdFFRanpzcVZtUkl2cDFEQllwMVJxMEp3TGpXeVJLK3daNGJPOFEyOGF3Z3BRQTFTSXQ5aEw3WHhRbjFkaWI1RDA1QnY0dDZ2bEg5VVoyS2psdEMwSU81VStWdjBmZzUrZlBIQmlHNks0TFRVRmU0NFl2alNneDVzWTNOdDJ2aWZDU2pPcit2NmR6Z1o0ckF5WDB2Q3l1SVJDdnZrMmFDdGZ2OFIvaVdObllMUUtFd1MzaXlyWmtWWERDc3g3cnlZd2Z1ZGJLdmZBQ0VId0lORzd1L1E4QjBGRDV6NnVwRjZFN0RHNG8zVWZnNWpuTDFBVmJoMkI0aVVkcUtWdmUybzFWV3NqRys3cmI4STUzaVRzaGhsT1prbmpHS2xndVUzblBBZXMyd29wR2R2d3Yyd0Y2NmNGaEtZSFhEdGhjNk04WS9Kai9JTEZ0OUhoNXFFeDVxU0N2QzJTaGxoRE14UGV1aDgxaTgiLCJtYWMiOiJlODMyY2I0Y2QzYzcxZmMxZTlmZGNhMmQ4MDFiNGE5NTQ1NWYwNzA1YmFkODg5NDIwMWE5OWRkMjg0MzMyMDc5IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:06:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InpidW1zS2NZa1BtazJnVzI3MXlXeHc9PSIsInZhbHVlIjoiVVA4MnczT3JTVGJVaTBtdkdkdW9Gd1dYd1NEMk5IMTVkMGlVUXpQbWxDSzhkWTJQVThhYlZrbHNqUS9velBHeTd1TEY3dXorR2tMTEkvZzQvZmRnWFpROFRWSDFnQjBWak9yc0Y3THBiUzRLVnZpQWVscVRNMmVnbFptSEU4NTR0aGJKcjBTMmp5NGM5cmpleTRUcStINjlVMlZjUW9TNXI5T05DWitXaVVWK3FDOFFoU1JlQThSQmF6dTVLZmllWGw3VWc0Um8rL3NQekVkK0VlZU5EZzhDRHYxQ0JHZW9RMy9Md0dBa1gvakd0aHo2UDhlaXd5YU5FREdiNlRGQWw1c3lhRWJBVVBzMEVsazJ0N05kMTZuODM2WWpXQWU3M0JlL3F1TVBGMGF5Z2dNMDVRMGpLUTVkdGhyVVlzbVJpODdzU0N1b0F4RG1teE5sTjFITU9hTTNMRDAzMkpvaUFFNWRLSy9IaUxFMzJnbmhsWFVxWVJnRFdLTytzNjJiOVVXVU5KUFV0ekhTKzRtN0RkMGZqSzVhY3VWU3krUjg2cmhYUDlGMmxJNnJtZzMvOXQ2YUxmMTN2RHFvaWMwelpscnNuMjhkUGUzNTAvVGNidWNOMUdoRmRhbkFmSk9PUStBTEp1NFpoZTJDeTJLWnltRU9GU2hTdUFEUlB3VXYiLCJtYWMiOiJkNWY3MDViODg3MjhkN2Y0N2E1ODRkNDNkM2Y1NGQ5ZTFkNWFjMmI4ZDI1MTY1Y2M4NDhjMTc1OTE3MDVmZmY3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:06:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlRIdDFGSTJsdUhsUisvSDE4QU5lRVE9PSIsInZhbHVlIjoiL2tFemZXdkJ1UnlNSFZ2OEgrdnpoNDhpU0Q0cVdNdjMyM1lxVFl5T2RPRFY1emFsUU5lQ21GMmUrMk5LUmR1VDVKSmNLWlA2MDNZOEh0L1dtZFRxbW1oaU5sbWhydlNmd3ViZVVFZjNOeDB3RDRuRG41dFlkVmZYYXpTUkNOOERTdGZ4QnZ5YTR3SWJadG9LODVWcUlyR1lKdFFRanpzcVZtUkl2cDFEQllwMVJxMEp3TGpXeVJLK3daNGJPOFEyOGF3Z3BRQTFTSXQ5aEw3WHhRbjFkaWI1RDA1QnY0dDZ2bEg5VVoyS2psdEMwSU81VStWdjBmZzUrZlBIQmlHNks0TFRVRmU0NFl2alNneDVzWTNOdDJ2aWZDU2pPcit2NmR6Z1o0ckF5WDB2Q3l1SVJDdnZrMmFDdGZ2OFIvaVdObllMUUtFd1MzaXlyWmtWWERDc3g3cnlZd2Z1ZGJLdmZBQ0VId0lORzd1L1E4QjBGRDV6NnVwRjZFN0RHNG8zVWZnNWpuTDFBVmJoMkI0aVVkcUtWdmUybzFWV3NqRys3cmI4STUzaVRzaGhsT1prbmpHS2xndVUzblBBZXMyd29wR2R2d3Yyd0Y2NmNGaEtZSFhEdGhjNk04WS9Kai9JTEZ0OUhoNXFFeDVxU0N2QzJTaGxoRE14UGV1aDgxaTgiLCJtYWMiOiJlODMyY2I0Y2QzYzcxZmMxZTlmZGNhMmQ4MDFiNGE5NTQ1NWYwNzA1YmFkODg5NDIwMWE5OWRkMjg0MzMyMDc5IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:06:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InpidW1zS2NZa1BtazJnVzI3MXlXeHc9PSIsInZhbHVlIjoiVVA4MnczT3JTVGJVaTBtdkdkdW9Gd1dYd1NEMk5IMTVkMGlVUXpQbWxDSzhkWTJQVThhYlZrbHNqUS9velBHeTd1TEY3dXorR2tMTEkvZzQvZmRnWFpROFRWSDFnQjBWak9yc0Y3THBiUzRLVnZpQWVscVRNMmVnbFptSEU4NTR0aGJKcjBTMmp5NGM5cmpleTRUcStINjlVMlZjUW9TNXI5T05DWitXaVVWK3FDOFFoU1JlQThSQmF6dTVLZmllWGw3VWc0Um8rL3NQekVkK0VlZU5EZzhDRHYxQ0JHZW9RMy9Md0dBa1gvakd0aHo2UDhlaXd5YU5FREdiNlRGQWw1c3lhRWJBVVBzMEVsazJ0N05kMTZuODM2WWpXQWU3M0JlL3F1TVBGMGF5Z2dNMDVRMGpLUTVkdGhyVVlzbVJpODdzU0N1b0F4RG1teE5sTjFITU9hTTNMRDAzMkpvaUFFNWRLSy9IaUxFMzJnbmhsWFVxWVJnRFdLTytzNjJiOVVXVU5KUFV0ekhTKzRtN0RkMGZqSzVhY3VWU3krUjg2cmhYUDlGMmxJNnJtZzMvOXQ2YUxmMTN2RHFvaWMwelpscnNuMjhkUGUzNTAvVGNidWNOMUdoRmRhbkFmSk9PUStBTEp1NFpoZTJDeTJLWnltRU9GU2hTdUFEUlB3VXYiLCJtYWMiOiJkNWY3MDViODg3MjhkN2Y0N2E1ODRkNDNkM2Y1NGQ5ZTFkNWFjMmI4ZDI1MTY1Y2M4NDhjMTc1OTE3MDVmZmY3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:06:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-105944173 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-105944173\", {\"maxDepth\":0})</script>\n"}}