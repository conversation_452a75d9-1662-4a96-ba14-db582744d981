{"__meta": {"id": "X7706dec969fe3504472b84e6a76a2e25", "datetime": "2025-06-17 12:06:48", "utime": **********.509091, "method": "GET", "uri": "/pos?warehouse_id=8&ajax=1&_=1750162004180", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750162007.713364, "end": **********.509115, "duration": 0.7957510948181152, "duration_str": "796ms", "measures": [{"label": "Booting", "start": 1750162007.713364, "relative_start": 0, "end": **********.321058, "relative_end": **********.321058, "duration": 0.607694149017334, "duration_str": "608ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.32108, "relative_start": 0.6077160835266113, "end": **********.509117, "relative_end": 1.9073486328125e-06, "duration": 0.18803691864013672, "duration_str": "188ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53057728, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET pos", "middleware": "web, verified, auth, XSS, revalidate", "as": "pos.index", "controller": "App\\Http\\Controllers\\PosController@index", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FPosController.php&line=37\" onclick=\"\">app/Http/Controllers/PosController.php:37-123</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.019530000000000002, "accumulated_duration_str": "19.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.390523, "duration": 0.00458, "duration_str": "4.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 23.451}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.41003, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 23.451, "width_percent": 4.557}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.4366221, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 28.008, "width_percent": 8.705}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.441808, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 36.713, "width_percent": 7.578}, {"sql": "select *, CONCAT(name) AS name from `warehouses` where `created_by` = 15 and `id` = 8", "type": "query", "params": [], "bindings": ["15", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\PosController.php", "line": 50}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4529421, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "PosController.php:50", "source": "app/Http/Controllers/PosController.php:50", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FPosController.php&line=50", "ajax": false, "filename": "PosController.php", "line": "50"}, "connection": "ty", "start_percent": 44.291, "width_percent": 7.015}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'ty' and table_name = 'customers' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\PosController.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.458726, "duration": 0.004030000000000001, "duration_str": "4.03ms", "memory": 0, "memory_str": null, "filename": "PosController.php:57", "source": "app/Http/Controllers/PosController.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FPosController.php&line=57", "ajax": false, "filename": "PosController.php", "line": "57"}, "connection": "ty", "start_percent": 51.306, "width_percent": 20.635}, {"sql": "select * from `customers` where `created_by` = 15 and (`warehouse_id` = '8' or `warehouse_id` is null or `warehouse_id` = '')", "type": "query", "params": [], "bindings": ["15", "8", ""], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\PosController.php", "line": 69}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.465486, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "PosController.php:69", "source": "app/Http/Controllers/PosController.php:69", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FPosController.php&line=69", "ajax": false, "filename": "PosController.php", "line": "69"}, "connection": "ty", "start_percent": 71.941, "width_percent": 8.602}, {"sql": "select * from `users` where (exists (select * from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `users`.`id` = `model_has_roles`.`model_id` and `model_has_roles`.`model_type` = 'App\\Models\\User' and `name` = 'delivery') or exists (select * from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `users`.`id` = `model_has_permissions`.`model_id` and `model_has_permissions`.`model_type` = 'App\\Models\\User' and `name` = 'manage delevery')) and (`warehouse_id` = 8 or `warehouse_id` is null or `warehouse_id` = '')", "type": "query", "params": [], "bindings": ["App\\Models\\User", "delivery", "App\\Models\\User", "manage delevery", "8", ""], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\PosController.php", "line": 85}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.470806, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "PosController.php:85", "source": "app/Http/Controllers/PosController.php:85", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FPosController.php&line=85", "ajax": false, "filename": "PosController.php", "line": "85"}, "connection": "ty", "start_percent": 80.543, "width_percent": 8.5}, {"sql": "select * from `pos` where `created_by` = 15 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\PosController.php", "line": 592}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\PosController.php", "line": 88}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.477818, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "PosController.php:592", "source": "app/Http/Controllers/PosController.php:592", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FPosController.php&line=592", "ajax": false, "filename": "PosController.php", "line": "592"}, "connection": "ty", "start_percent": 89.042, "width_percent": 10.958}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}, "App\\Models\\Pos": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}}, "count": 7, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1064608550 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1064608550\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.450768, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1796069683 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1796069683\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.476443, "xdebug_link": null}]}, "session": {"_token": "8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/pos", "status_code": "<pre class=sf-dump id=sf-dump-2428760 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2428760\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1805013393 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>ajax</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">1750162004180</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1805013393\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-544980337 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-544980337\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-888147497 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=1ta1y4p%7C1750157552093%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlRIdDFGSTJsdUhsUisvSDE4QU5lRVE9PSIsInZhbHVlIjoiL2tFemZXdkJ1UnlNSFZ2OEgrdnpoNDhpU0Q0cVdNdjMyM1lxVFl5T2RPRFY1emFsUU5lQ21GMmUrMk5LUmR1VDVKSmNLWlA2MDNZOEh0L1dtZFRxbW1oaU5sbWhydlNmd3ViZVVFZjNOeDB3RDRuRG41dFlkVmZYYXpTUkNOOERTdGZ4QnZ5YTR3SWJadG9LODVWcUlyR1lKdFFRanpzcVZtUkl2cDFEQllwMVJxMEp3TGpXeVJLK3daNGJPOFEyOGF3Z3BRQTFTSXQ5aEw3WHhRbjFkaWI1RDA1QnY0dDZ2bEg5VVoyS2psdEMwSU81VStWdjBmZzUrZlBIQmlHNks0TFRVRmU0NFl2alNneDVzWTNOdDJ2aWZDU2pPcit2NmR6Z1o0ckF5WDB2Q3l1SVJDdnZrMmFDdGZ2OFIvaVdObllMUUtFd1MzaXlyWmtWWERDc3g3cnlZd2Z1ZGJLdmZBQ0VId0lORzd1L1E4QjBGRDV6NnVwRjZFN0RHNG8zVWZnNWpuTDFBVmJoMkI0aVVkcUtWdmUybzFWV3NqRys3cmI4STUzaVRzaGhsT1prbmpHS2xndVUzblBBZXMyd29wR2R2d3Yyd0Y2NmNGaEtZSFhEdGhjNk04WS9Kai9JTEZ0OUhoNXFFeDVxU0N2QzJTaGxoRE14UGV1aDgxaTgiLCJtYWMiOiJlODMyY2I0Y2QzYzcxZmMxZTlmZGNhMmQ4MDFiNGE5NTQ1NWYwNzA1YmFkODg5NDIwMWE5OWRkMjg0MzMyMDc5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InpidW1zS2NZa1BtazJnVzI3MXlXeHc9PSIsInZhbHVlIjoiVVA4MnczT3JTVGJVaTBtdkdkdW9Gd1dYd1NEMk5IMTVkMGlVUXpQbWxDSzhkWTJQVThhYlZrbHNqUS9velBHeTd1TEY3dXorR2tMTEkvZzQvZmRnWFpROFRWSDFnQjBWak9yc0Y3THBiUzRLVnZpQWVscVRNMmVnbFptSEU4NTR0aGJKcjBTMmp5NGM5cmpleTRUcStINjlVMlZjUW9TNXI5T05DWitXaVVWK3FDOFFoU1JlQThSQmF6dTVLZmllWGw3VWc0Um8rL3NQekVkK0VlZU5EZzhDRHYxQ0JHZW9RMy9Md0dBa1gvakd0aHo2UDhlaXd5YU5FREdiNlRGQWw1c3lhRWJBVVBzMEVsazJ0N05kMTZuODM2WWpXQWU3M0JlL3F1TVBGMGF5Z2dNMDVRMGpLUTVkdGhyVVlzbVJpODdzU0N1b0F4RG1teE5sTjFITU9hTTNMRDAzMkpvaUFFNWRLSy9IaUxFMzJnbmhsWFVxWVJnRFdLTytzNjJiOVVXVU5KUFV0ekhTKzRtN0RkMGZqSzVhY3VWU3krUjg2cmhYUDlGMmxJNnJtZzMvOXQ2YUxmMTN2RHFvaWMwelpscnNuMjhkUGUzNTAvVGNidWNOMUdoRmRhbkFmSk9PUStBTEp1NFpoZTJDeTJLWnltRU9GU2hTdUFEUlB3VXYiLCJtYWMiOiJkNWY3MDViODg3MjhkN2Y0N2E1ODRkNDNkM2Y1NGQ5ZTFkNWFjMmI4ZDI1MTY1Y2M4NDhjMTc1OTE3MDVmZmY3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-888147497\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1827108296 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">x6J25WmpCphH0nVhGWCYfOOOMFtTSb6zPGzaM2hl</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1827108296\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1661073810 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 12:06:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikhzc3o5djlFSEFuQVJJOHlCN3diSWc9PSIsInZhbHVlIjoic29kdVhkSHREL0tLUzBwd0FaQWl3Q25GK3R1U3kyejllcGxhYUV2VFVvUUdQc1RYL2Jhd2wraWFyWW5RakJFWVN6S1I0a2lXWlNtQVpCbys5ZEpBaFZsaElDS0gwcDFmdEFhTzlDN0NVdk9JeGxSb1BzOGhkbndRRzhhTlh2WHF5ZEFoQ1N2QTY4dGZnaXo1b1hjZzMxN3RoTGZlMEo2UUpyNUR5dnR0cXA3T25kWkxtcjgwd1dEcWFCK1Vzc1oyRlFXbm81T3FFZGJNYktHS0I4WW1OMy9TYlpmRzJGYmxRQThseThiL3ZTMlRqYzRtMjVHOS9HZmd1N2hCTUFhaVl6OEdVaTZEMFBJVEYyOGVWTHlqQnFEVDlLREZFaGxjckxnVFZnblE3RFYwUmUxM2hBYS92NGQ3Y0kveElJNEpiZUxRVUhpNFJBM1FJampCY3hzL3ZVOHpaWHpHYVNlM0psL2hLRnRoVC8vd0dUdit4bjRSYjl4UFlRQ0N2eU8vWkN4Y3Y3VS93cStLZ0lvbjRyRXpDcysrMVhDa2VYMHJ6RTRBVW91MC9PcVpKbmplQ2U5WGVKV0kxME51d3VxUGR6dTlCbXQ5TDFudmtwK0pHdmJrR1ZrMnZaMmVCOHQxdlovOU4rbUtwQlFER1ZPd0ZkYkJZbFRzZEFYM3lxaHMiLCJtYWMiOiI0NGI3ZmQ4Mzc2NjdkNTAzYjM0YWU4NDBlOWY2YzY1ZTQwZWQxM2U2ODkxZWM3NmM2ZjE5YWRjYTNlY2UzOTgwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:06:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkljNTg4QWlBY29WUjNPQ3RYSnNCTlE9PSIsInZhbHVlIjoiajkwTVVXeGM4WVN5NnRTSERBUEx4dG5QSE4rbHhHMXp1a1JGTU82Y3phQjhYejR6bkNVSkJqZkZrVEpKZkRLUnU3V0xZbkJnR04rTU5mR1YydHNkVG5jMjN3VkhYR1NjZU9zMzNLYllqL1k2Q0xIeFArcERiOGRQSUdwK3RuUUNRRVFxdkh4blVBQmxjb1FJbzcyU29yRzRYamlwQzlUVTV1U1BXRGNwUWQ5NERQbnVORVhSL013ZC9pNUdQNW1OdnJ2Y3NwaGVrSGt5V0xLS0dTTTNWV2RxTDVHMzEvVVEyQ1owQnMvZjBJUjVHR3Zka3FOUVY4dUVFUlF2Qi9tS1FlRGp5cTJRZDZKaHVVMXAvd0QvWnprNHF0K3NTanJoTzJzTmN0bjBtZUZTczZOTWFwektHbVB5MzZOOGd2WGRrOFBNaHVPOC9VVjR2eFhRWFdxa1VweUFrMVlLSjJXVDM5WStKRy9sYWY3anV3aXUzNmNOdWJnNE5iSUY3ZjdWQnZVcHVJNGlYZFVnbURqUVRPYzBXWmM1QlJIV3VRRzRNMVFLZG0wTzNPSDJuanNYeDNXcy9vWVFwQ3Yvby9xa0ZoU3VmK3RaemMrTWV4VFhXdEpQaDU5WVp5UGhGTmZPM2crMHRROGhqNVlHNkRYSVhycUxLMmNkN0tyRDArWWEiLCJtYWMiOiI1NzYwYmVmMzQ1ZDkzOTZhNTIzNTJhNzNhNGY2MjkyMTYzMDU0ZmY5ODk2NjFjYTM1NDc1NDQxNTAyN2YzNjYxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:06:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikhzc3o5djlFSEFuQVJJOHlCN3diSWc9PSIsInZhbHVlIjoic29kdVhkSHREL0tLUzBwd0FaQWl3Q25GK3R1U3kyejllcGxhYUV2VFVvUUdQc1RYL2Jhd2wraWFyWW5RakJFWVN6S1I0a2lXWlNtQVpCbys5ZEpBaFZsaElDS0gwcDFmdEFhTzlDN0NVdk9JeGxSb1BzOGhkbndRRzhhTlh2WHF5ZEFoQ1N2QTY4dGZnaXo1b1hjZzMxN3RoTGZlMEo2UUpyNUR5dnR0cXA3T25kWkxtcjgwd1dEcWFCK1Vzc1oyRlFXbm81T3FFZGJNYktHS0I4WW1OMy9TYlpmRzJGYmxRQThseThiL3ZTMlRqYzRtMjVHOS9HZmd1N2hCTUFhaVl6OEdVaTZEMFBJVEYyOGVWTHlqQnFEVDlLREZFaGxjckxnVFZnblE3RFYwUmUxM2hBYS92NGQ3Y0kveElJNEpiZUxRVUhpNFJBM1FJampCY3hzL3ZVOHpaWHpHYVNlM0psL2hLRnRoVC8vd0dUdit4bjRSYjl4UFlRQ0N2eU8vWkN4Y3Y3VS93cStLZ0lvbjRyRXpDcysrMVhDa2VYMHJ6RTRBVW91MC9PcVpKbmplQ2U5WGVKV0kxME51d3VxUGR6dTlCbXQ5TDFudmtwK0pHdmJrR1ZrMnZaMmVCOHQxdlovOU4rbUtwQlFER1ZPd0ZkYkJZbFRzZEFYM3lxaHMiLCJtYWMiOiI0NGI3ZmQ4Mzc2NjdkNTAzYjM0YWU4NDBlOWY2YzY1ZTQwZWQxM2U2ODkxZWM3NmM2ZjE5YWRjYTNlY2UzOTgwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:06:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkljNTg4QWlBY29WUjNPQ3RYSnNCTlE9PSIsInZhbHVlIjoiajkwTVVXeGM4WVN5NnRTSERBUEx4dG5QSE4rbHhHMXp1a1JGTU82Y3phQjhYejR6bkNVSkJqZkZrVEpKZkRLUnU3V0xZbkJnR04rTU5mR1YydHNkVG5jMjN3VkhYR1NjZU9zMzNLYllqL1k2Q0xIeFArcERiOGRQSUdwK3RuUUNRRVFxdkh4blVBQmxjb1FJbzcyU29yRzRYamlwQzlUVTV1U1BXRGNwUWQ5NERQbnVORVhSL013ZC9pNUdQNW1OdnJ2Y3NwaGVrSGt5V0xLS0dTTTNWV2RxTDVHMzEvVVEyQ1owQnMvZjBJUjVHR3Zka3FOUVY4dUVFUlF2Qi9tS1FlRGp5cTJRZDZKaHVVMXAvd0QvWnprNHF0K3NTanJoTzJzTmN0bjBtZUZTczZOTWFwektHbVB5MzZOOGd2WGRrOFBNaHVPOC9VVjR2eFhRWFdxa1VweUFrMVlLSjJXVDM5WStKRy9sYWY3anV3aXUzNmNOdWJnNE5iSUY3ZjdWQnZVcHVJNGlYZFVnbURqUVRPYzBXWmM1QlJIV3VRRzRNMVFLZG0wTzNPSDJuanNYeDNXcy9vWVFwQ3Yvby9xa0ZoU3VmK3RaemMrTWV4VFhXdEpQaDU5WVp5UGhGTmZPM2crMHRROGhqNVlHNkRYSVhycUxLMmNkN0tyRDArWWEiLCJtYWMiOiI1NzYwYmVmMzQ1ZDkzOTZhNTIzNTJhNzNhNGY2MjkyMTYzMDU0ZmY5ODk2NjFjYTM1NDc1NDQxNTAyN2YzNjYxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:06:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1661073810\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2042825094 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2042825094\", {\"maxDepth\":0})</script>\n"}}