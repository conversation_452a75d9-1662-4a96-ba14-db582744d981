<?php
/**
 * إصلاح سريع لمشكلة MySQL sql_mode
 * يحل مشكلة NO_AUTO_CREATE_USER في MySQL 8.0+
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 إصلاح مشكلة MySQL sql_mode</h1>";
echo "<hr>";

// قراءة إعدادات قاعدة البيانات من .env
$envFile = __DIR__ . '/../.env';
if (!file_exists($envFile)) {
    echo "<p style='color: red;'>❌ ملف .env غير موجود</p>";
    exit;
}

$envContent = file_get_contents($envFile);
preg_match('/DB_HOST=(.*)/', $envContent, $hostMatch);
preg_match('/DB_PORT=(.*)/', $envContent, $portMatch);
preg_match('/DB_DATABASE=(.*)/', $envContent, $dbMatch);
preg_match('/DB_USERNAME=(.*)/', $envContent, $userMatch);
preg_match('/DB_PASSWORD=(.*)/', $envContent, $passMatch);

$host = trim($hostMatch[1] ?? '127.0.0.1');
$port = trim($portMatch[1] ?? '3306');
$database = trim($dbMatch[1] ?? '');
$username = trim($userMatch[1] ?? 'root');
$password = trim($passMatch[1] ?? '');

echo "<h2>📊 معلومات قاعدة البيانات:</h2>";
echo "<ul>";
echo "<li><strong>الخادم:</strong> $host:$port</li>";
echo "<li><strong>قاعدة البيانات:</strong> $database</li>";
echo "<li><strong>المستخدم:</strong> $username</li>";
echo "</ul>";

try {
    // الاتصال بقاعدة البيانات
    $dsn = "mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    // فحص إصدار MySQL
    $stmt = $pdo->query("SELECT VERSION() as version");
    $version = $stmt->fetch(PDO::FETCH_ASSOC)['version'];
    echo "<p><strong>إصدار MySQL:</strong> $version</p>";
    
    // فحص sql_mode الحالي
    $stmt = $pdo->query("SELECT @@sql_mode as sql_mode");
    $currentSqlMode = $stmt->fetch(PDO::FETCH_ASSOC)['sql_mode'];
    echo "<p><strong>sql_mode الحالي:</strong> $currentSqlMode</p>";
    
    // فحص إذا كان يحتوي على NO_AUTO_CREATE_USER
    if (strpos($currentSqlMode, 'NO_AUTO_CREATE_USER') !== false) {
        echo "<p style='color: orange;'>⚠️ sql_mode يحتوي على NO_AUTO_CREATE_USER (مشكلة!)</p>";
        
        // إصلاح sql_mode
        $newSqlMode = str_replace('NO_AUTO_CREATE_USER,', '', $currentSqlMode);
        $newSqlMode = str_replace(',NO_AUTO_CREATE_USER', '', $newSqlMode);
        $newSqlMode = str_replace('NO_AUTO_CREATE_USER', '', $newSqlMode);
        
        try {
            $pdo->exec("SET sql_mode = '$newSqlMode'");
            echo "<p style='color: green;'>✅ تم إصلاح sql_mode مؤقتاً</p>";
            echo "<p><strong>sql_mode الجديد:</strong> $newSqlMode</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ فشل في إصلاح sql_mode: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ sql_mode لا يحتوي على NO_AUTO_CREATE_USER</p>";
    }
    
    // اختبار الاستعلام الذي كان يفشل
    echo "<h2>🧪 اختبار الاستعلام:</h2>";
    try {
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ? LIMIT 1");
        $stmt->execute([16]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            echo "<p style='color: green;'>✅ تم العثور على المستخدم: " . ($user['name'] ?? 'غير محدد') . "</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ لم يتم العثور على مستخدم بالمعرف 16</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ فشل الاستعلام: " . $e->getMessage() . "</p>";
    }
    
    // اختبار POS
    echo "<h2>🛒 اختبار جداول POS:</h2>";
    $posTables = ['pos', 'pos_payments', 'pos_products'];
    foreach ($posTables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            echo "<p>✅ جدول $table: $count سجل</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ مشكلة في جدول $table: " . $e->getMessage() . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في الاتصال: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h2>📋 خطوات الإصلاح الدائم:</h2>";
echo "<ol>";
echo "<li><strong>تم تحديث ملف config/database.php</strong> - إزالة NO_AUTO_CREATE_USER</li>";
echo "<li><strong>إعادة تشغيل الخادم</strong> (إذا أمكن)</li>";
echo "<li><strong>مسح الكاش:</strong>";
echo "<pre>php artisan config:clear\nphp artisan cache:clear</pre>";
echo "</li>";
echo "<li><strong>اختبار نظام POS</strong></li>";
echo "</ol>";

echo "<h2>🔧 إعدادات MySQL الموصى بها:</h2>";
echo "<p>إذا كان لديك صلاحية تعديل إعدادات MySQL، أضف هذا إلى my.cnf:</p>";
echo "<pre>";
echo "[mysqld]\n";
echo "sql_mode = \"STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION\"\n";
echo "</pre>";

echo "<h2>🚀 اختبار سريع لنظام POS:</h2>";
echo "<button onclick='testPOS()' style='padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer;'>اختبار POS الآن</button>";
echo "<div id='pos-test-result' style='margin-top: 10px;'></div>";

?>

<script>
function testPOS() {
    const resultDiv = document.getElementById('pos-test-result');
    resultDiv.innerHTML = '⏳ جاري اختبار نظام POS...';
    
    // محاولة الوصول لصفحة POS
    fetch('/pos', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (response.ok) {
            resultDiv.innerHTML = '✅ نظام POS يعمل بشكل طبيعي!';
            resultDiv.style.color = 'green';
            resultDiv.style.background = '#d4edda';
            resultDiv.style.padding = '10px';
            resultDiv.style.borderRadius = '5px';
        } else {
            resultDiv.innerHTML = '❌ مشكلة في الوصول لنظام POS: ' + response.status;
            resultDiv.style.color = 'red';
            resultDiv.style.background = '#f8d7da';
            resultDiv.style.padding = '10px';
            resultDiv.style.borderRadius = '5px';
        }
    })
    .catch(error => {
        resultDiv.innerHTML = '❌ خطأ في الاتصال: ' + error.message;
        resultDiv.style.color = 'red';
        resultDiv.style.background = '#f8d7da';
        resultDiv.style.padding = '10px';
        resultDiv.style.borderRadius = '5px';
    });
}
</script>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    background: #f8f9fa;
}
h1, h2 { 
    color: #333; 
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}
pre {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    border-left: 4px solid #007bff;
}
button:hover { 
    background: #218838 !important; 
}
</style>
